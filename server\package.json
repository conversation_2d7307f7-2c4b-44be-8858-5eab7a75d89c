{"name": "thescienceofpublicrelations-server", "version": "1.0.0", "description": "Server for The Science of Public Relations website", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^4.21.2", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.8.7", "multer": "^2.0.1", "nodemailer": "^6.10.1"}, "devDependencies": {"nodemon": "^3.0.1"}}