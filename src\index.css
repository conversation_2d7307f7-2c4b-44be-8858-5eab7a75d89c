@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom utilities */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@layer base {
  html {
    scroll-behavior: smooth;
    -webkit-tap-highlight-color: transparent;
  }

  body {
    font-family: 'Futura', 'Inter', sans-serif;
    @apply text-gray-800 overflow-x-hidden;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }

  h1 {
    @apply text-3xl sm:text-4xl md:text-5xl;
  }

  h2 {
    @apply text-2xl sm:text-3xl md:text-4xl;
  }

  h3 {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  p {
    @apply text-base sm:text-lg;
  }
}

@layer components {
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .section-spacing {
    @apply py-12 sm:py-16 md:py-20;
  }

  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300;
  }

  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-md transition-all duration-300 font-medium;
  }

  .btn-primary {
    @apply btn bg-blue-600 hover:bg-blue-700 text-white shadow-md;
  }

  .btn-secondary {
    @apply btn bg-yellow-500 hover:bg-yellow-600 text-white shadow-md;
  }

  .btn-outline {
    @apply btn border border-gray-300 hover:bg-gray-50 text-gray-700;
  }

  .input {
    @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-300;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 1s ease-in-out;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  .responsive-grid-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6;
  }

  .responsive-flex {
    @apply flex flex-col md:flex-row;
  }

  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .touch-safe {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }
  
  .mobile-container {
    @apply px-4 sm:px-6 lg:px-8 mx-auto max-w-7xl;
  }
  
  .mobile-section {
    @apply py-12 sm:py-16 md:py-20;
  }
  
  .mobile-text {
    @apply text-base sm:text-lg md:text-xl;
  }
}