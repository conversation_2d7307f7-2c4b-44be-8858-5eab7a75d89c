<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Paystack Access Code Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    button {
      background-color: #0066cc;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 10px;
    }
    button:hover {
      background-color: #0055aa;
    }
    .status {
      margin-top: 20px;
      padding: 10px;
      border-radius: 4px;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
    .info {
      background-color: #d1ecf1;
      color: #0c5460;
    }
    input, textarea {
      width: 100%;
      padding: 8px;
      margin-top: 5px;
      margin-bottom: 15px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    label {
      font-weight: bold;
    }
    .code {
      font-family: monospace;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>Paystack Access Code Test</h1>

  <div>
    <p>This page tests Paystack integration using access codes.</p>

    <div>
      <h3>1. Initialize Transaction</h3>
      <form id="initForm">
        <div>
          <label for="email">Email Address:</label>
          <input type="email" id="email" value="<EMAIL>">
        </div>
        <div>
          <label for="amount">Amount (NGN):</label>
          <input type="number" id="amount" value="2999">
        </div>
        <div>
          <label for="secretKey">Paystack Secret Key:</label>
          <input type="text" id="secretKey" value="************************************************">
        </div>
        <button type="button" id="initButton">Initialize Transaction</button>
      </form>
      <div id="initStatus" class="status"></div>
      <div id="initResponse" class="code"></div>
    </div>

    <div style="margin-top: 30px;">
      <h3>2. Test Direct Checkout</h3>
      <div>
        <label for="accessCode">Access Code:</label>
        <input type="text" id="accessCode" placeholder="Enter access code from step 1">
        <button type="button" id="checkoutButton">Open Checkout Page</button>
      </div>
    </div>

    <div style="margin-top: 30px;">
      <h3>3. Verify Transaction</h3>
      <div>
        <label for="reference">Reference:</label>
        <input type="text" id="reference" placeholder="Enter transaction reference">
        <button type="button" id="verifyButton">Verify Transaction</button>
      </div>
      <div id="verifyStatus" class="status"></div>
      <div id="verifyResponse" class="code"></div>
    </div>
  </div>

  <script>
    // Initialize Transaction
    document.getElementById('initButton').addEventListener('click', async () => {
      const email = document.getElementById('email').value;
      const amount = document.getElementById('amount').value;
      const secretKey = document.getElementById('secretKey').value;
      const initStatus = document.getElementById('initStatus');
      const initResponse = document.getElementById('initResponse');

      if (!email || !amount || !secretKey) {
        initStatus.textContent = 'Please fill in all fields';
        initStatus.className = 'status error';
        return;
      }

      initStatus.textContent = 'Initializing transaction...';
      initStatus.className = 'status info';

      try {
        const response = await fetch('https://api.paystack.co/transaction/initialize', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${secretKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email,
            amount: parseInt(amount) * 100, // Convert to kobo
            metadata: {
              product_name: 'Test Product',
              is_test: true
            }
          })
        });

        const data = await response.json();

        if (data.status) {
          initStatus.textContent = 'Transaction initialized successfully!';
          initStatus.className = 'status success';

          // Auto-fill the access code field
          if (data.data.access_code) {
            document.getElementById('accessCode').value = data.data.access_code;
          }

          // Auto-fill the reference field
          if (data.data.reference) {
            document.getElementById('reference').value = data.data.reference;
          }
        } else {
          initStatus.textContent = 'Failed to initialize transaction: ' + data.message;
          initStatus.className = 'status error';
        }

        // Display the full response
        initResponse.textContent = JSON.stringify(data, null, 2);
      } catch (error) {
        console.error('Error:', error);
        initStatus.textContent = 'Error: ' + error.message;
        initStatus.className = 'status error';
      }
    });

    // Open Checkout Page
    document.getElementById('checkoutButton').addEventListener('click', () => {
      const accessCode = document.getElementById('accessCode').value;

      if (!accessCode) {
        alert('Please enter an access code');
        return;
      }

      window.open(`https://checkout.paystack.com/${accessCode}`, '_blank');
    });

    // Verify Transaction
    document.getElementById('verifyButton').addEventListener('click', async () => {
      const reference = document.getElementById('reference').value;
      const secretKey = document.getElementById('secretKey').value;
      const verifyStatus = document.getElementById('verifyStatus');
      const verifyResponse = document.getElementById('verifyResponse');

      if (!reference || !secretKey) {
        verifyStatus.textContent = 'Please enter a reference and secret key';
        verifyStatus.className = 'status error';
        return;
      }

      verifyStatus.textContent = 'Verifying transaction...';
      verifyStatus.className = 'status info';

      try {
        const response = await fetch(`https://api.paystack.co/transaction/verify/${reference}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${secretKey}`
          }
        });

        const data = await response.json();

        if (data.status) {
          verifyStatus.textContent = `Transaction status: ${data.data.status}`;
          verifyStatus.className = 'status success';
        } else {
          verifyStatus.textContent = 'Failed to verify transaction: ' + data.message;
          verifyStatus.className = 'status error';
        }

        // Display the full response
        verifyResponse.textContent = JSON.stringify(data, null, 2);
      } catch (error) {
        console.error('Error:', error);
        verifyStatus.textContent = 'Error: ' + error.message;
        verifyStatus.className = 'status error';
      }
    });
  </script>
</body>
</html>
